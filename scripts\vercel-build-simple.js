#!/usr/bin/env node

/**
 * Simplified Vercel Build Script for web-driver
 * 
 * This script uses a simpler approach that's more compatible with Vercel's build environment
 */

const { execSync } = require('child_process');

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    build: '🔨'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function execCommand(command, options = {}) {
  log(`Executing: ${command}`, 'build');
  try {
    const result = execSync(command, {
      stdio: 'inherit',
      cwd: process.cwd(),
      ...options
    });
    return result;
  } catch (error) {
    log(`Command failed: ${command}`, 'error');
    throw error;
  }
}

function main() {
  log('Starting Simplified Vercel Build for web-driver', 'info');
  
  try {
    // Set Vercel-specific environment variables
    process.env.VERCEL = '1';
    process.env.NEXT_TELEMETRY_DISABLED = '1';
    process.env.SKIP_ENV_VALIDATION = '1';
    process.env.NODE_OPTIONS = '--max-old-space-size=4096';
    
    log('Environment variables set', 'success');
    
    // Build workspace packages first using Turborepo
    log('Building workspace dependencies...', 'info');
    execCommand('pnpm turbo build --filter=shared-types --filter=shared-utils --filter=config --filter=firebase-config');
    
    log('Building remaining workspace packages...', 'info');
    execCommand('pnpm turbo build --filter=shared-ui --filter=api-client --filter=business-logic');
    
    // Build web-driver
    log('Building web-driver application...', 'info');
    execCommand('pnpm turbo build --filter=web-driver');
    
    log('🎉 Build completed successfully!', 'success');
    
  } catch (error) {
    log(`Build failed: ${error.message}`, 'error');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
