{"framework": "nextjs", "regions": ["sin1"], "functions": {"src/app/api/**/*.{js,ts,tsx}": {"maxDuration": 30}}, "build": {"env": {"NODE_OPTIONS": "--max-old-space-size=4096", "VERCEL": "1", "NEXT_TELEMETRY_DISABLED": "1", "SKIP_ENV_VALIDATION": "1", "TURBO_TEAM": "team_tap2go", "TURBO_TOKEN": "$TURBO_TOKEN"}}, "installCommand": "cd ../.. && pnpm install --no-frozen-lockfile", "buildCommand": "cd ../.. && node scripts/vercel-build-driver.js", "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}]}