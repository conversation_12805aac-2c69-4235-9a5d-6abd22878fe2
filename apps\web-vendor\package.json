{"name": "web-vendor", "version": "0.1.0", "private": true, "scripts": {"dev": "pnpm exec next dev --turbopack --port 3001", "build": "npm run patch-styled-jsx && npx next build", "start": "pnpm exec next start --port 3001", "lint": "pnpm exec next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next", "patch-styled-jsx": "node scripts/patch-styled-jsx.js", "postinstall": "npm run patch-styled-jsx"}, "dependencies": {"@cloudinary/react": "^1.14.3", "@cloudinary/url-gen": "^1.21.0", "@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.4", "@supabase/supabase-js": "^2.50.0", "@types/react": "~19.0.10", "@types/react-dom": "^19.1.0", "@upstash/redis": "^1.35.0", "api-client": "workspace:*", "axios": "^1.9.0", "business-logic": "workspace:*", "cloudinary": "^2.6.1", "config": "workspace:*", "firebase": "^10.13.1", "lucide-react": "^0.445.0", "next": "15.3.2", "react": "19.0.0", "react-dom": "19.0.0", "resend": "^4.0.1", "shared-types": "workspace:*", "shared-ui": "workspace:*", "shared-utils": "workspace:*", "firebase-config": "workspace:*"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "15.3.2", "postcss": "^8.4.47", "tailwindcss": "^3.4.12", "typescript": "^5"}}