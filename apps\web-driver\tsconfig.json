{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/lib/*": ["./src/lib/*"], "@/lib/firebase": ["./src/lib/firebase.ts"], "@/lib/firebase-admin": ["./src/lib/firebase-admin.ts"], "@/lib/database/users": ["./src/lib/database/users.ts"], "@/components/*": ["./src/components/*"], "@/contexts/*": ["./src/contexts/*"], "@/types/*": ["./src/types/*"], "@/app/*": ["./src/app/*"], "@/hooks/*": ["./src/hooks/*"], "@/utils/*": ["./src/utils/*"]}, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "typeRoots": ["../../node_modules/@types", "./src/types"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/types/**/*.d.ts", "src/types/**/*.ts"], "exclude": ["node_modules"]}