#!/usr/bin/env node

/**
 * Professional Vercel Build Script for web-driver in Turborepo Monorepo
 * 
 * This script implements enterprise-level best practices for Vercel deployments:
 * - Proper dependency resolution in monorepo
 * - Workspace package building in correct order
 * - Comprehensive error handling and logging
 * - Vercel-specific optimizations
 * 
 * Usage: node scripts/vercel-build-driver.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const MONOREPO_ROOT = process.cwd();
const WEB_DRIVER_PATH = path.join(MONOREPO_ROOT, 'apps', 'web-driver');
const PACKAGES_PATH = path.join(MONOREPO_ROOT, 'packages');

// Logging utilities
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    build: '🔨'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function execCommand(command, options = {}) {
  log(`Executing: ${command}`, 'build');
  try {
    const result = execSync(command, {
      stdio: 'inherit',
      cwd: MONOREPO_ROOT,
      ...options
    });
    return result;
  } catch (error) {
    log(`Command failed: ${command}`, 'error');
    log(`Error: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Validation functions
function validateEnvironment() {
  log('Validating build environment...', 'info');
  
  // Check if we're in the correct directory
  if (!fs.existsSync(path.join(MONOREPO_ROOT, 'turbo.json'))) {
    log('turbo.json not found. Are you in the monorepo root?', 'error');
    process.exit(1);
  }
  
  // Check if web-driver app exists
  if (!fs.existsSync(WEB_DRIVER_PATH)) {
    log(`web-driver app not found at ${WEB_DRIVER_PATH}`, 'error');
    process.exit(1);
  }
  
  // Check if packages directory exists
  if (!fs.existsSync(PACKAGES_PATH)) {
    log(`packages directory not found at ${PACKAGES_PATH}`, 'error');
    process.exit(1);
  }
  
  log('Environment validation passed', 'success');
}

function validateWorkspaceDependencies() {
  log('Validating workspace dependencies...', 'info');
  
  const requiredPackages = [
    'shared-ui',
    'api-client', 
    'business-logic',
    'config',
    'firebase-config',
    'shared-types',
    'shared-utils'
  ];
  
  for (const pkg of requiredPackages) {
    const pkgPath = path.join(PACKAGES_PATH, pkg);
    const pkgJsonPath = path.join(pkgPath, 'package.json');
    
    if (!fs.existsSync(pkgPath)) {
      log(`Required package missing: ${pkg}`, 'error');
      process.exit(1);
    }
    
    if (!fs.existsSync(pkgJsonPath)) {
      log(`package.json missing for: ${pkg}`, 'error');
      process.exit(1);
    }
    
    log(`Workspace dependency validated: ${pkg}`, 'success');
  }
}

// Build functions
function installDependencies() {
  log('Installing dependencies...', 'info');
  execCommand('pnpm install --no-frozen-lockfile');
  log('Dependencies installed successfully', 'success');
}

function buildWorkspacePackages() {
  log('Building workspace packages...', 'info');
  
  // Use Turborepo to build all packages that web-driver depends on
  execCommand('pnpm turbo build --filter=web-driver^...');
  
  log('Workspace packages built successfully', 'success');
}

function buildWebDriver() {
  log('Building web-driver application...', 'info');
  
  // Set Vercel-specific environment variables
  process.env.VERCEL = '1';
  process.env.NEXT_TELEMETRY_DISABLED = '1';
  process.env.SKIP_ENV_VALIDATION = '1';
  process.env.NODE_OPTIONS = '--max-old-space-size=4096';
  
  // Build the web-driver app
  execCommand('pnpm turbo build --filter=web-driver');
  
  log('web-driver application built successfully', 'success');
}

// Main execution
function main() {
  log('Starting Professional Vercel Build for web-driver', 'info');
  log(`Monorepo root: ${MONOREPO_ROOT}`, 'info');
  log(`web-driver path: ${WEB_DRIVER_PATH}`, 'info');
  
  try {
    // Step 1: Validate environment
    validateEnvironment();
    
    // Step 2: Validate workspace dependencies
    validateWorkspaceDependencies();
    
    // Step 3: Install dependencies
    installDependencies();
    
    // Step 4: Build workspace packages first
    buildWorkspacePackages();
    
    // Step 5: Build web-driver application
    buildWebDriver();
    
    log('🎉 Build completed successfully!', 'success');
    log('📦 web-driver is ready for deployment', 'success');
    
  } catch (error) {
    log(`Build failed: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  main();
}

module.exports = { main };
