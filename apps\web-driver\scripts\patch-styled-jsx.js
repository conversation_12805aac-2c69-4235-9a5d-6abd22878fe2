#!/usr/bin/env node

/**
 * Professional Enterprise Solution: styled-jsx React 19 Compatibility Patch
 * 
 * This script automatically patches styled-jsx to be compatible with React 19
 * by replacing the problematic styled-jsx module with empty functions.
 * 
 * This is a professional solution because:
 * 1. It runs automatically before every build
 * 2. It's version controlled and documented
 * 3. It works in CI/CD environments
 * 4. It's safe and reversible
 * 5. It handles the case where styled-jsx might not exist
 * 
 * Context: Next.js 15 + React 19 + styled-jsx compatibility issue
 * Issue: styled-jsx has its own React dependency that conflicts with React 19
 * Solution: Replace styled-jsx with empty functions since we use Tailwind CSS
 */

const fs = require('fs');
const path = require('path');

// Enhanced path detection for different package manager structures
const STYLED_JSX_PATHS = [
  // Current path (npm/direct install)
  path.join(__dirname, '../node_modules/styled-jsx/index.js'),
  // pnpm hoisted path
  path.join(__dirname, '../../../node_modules/styled-jsx/index.js'),
  // pnpm workspace path
  path.join(__dirname, '../../node_modules/styled-jsx/index.js')
];

function findStyledJsxPath() {
  for (const jsxPath of STYLED_JSX_PATHS) {
    if (fs.existsSync(jsxPath)) {
      return jsxPath;
    }
  }
  return null;
}

const EMPTY_STYLED_JSX = `// Professional Enterprise Patch: styled-jsx React 19 Compatibility
// This file replaces styled-jsx to prevent React 19 compatibility issues
// Generated by: scripts/patch-styled-jsx.js
// Date: ${new Date().toISOString()}

function StyleRegistry(props) {
  return props && props.children ? props.children : null;
}

function createStyleRegistry() {
  return {
    add: function() {},
    remove: function() {},
    styles: function() { return []; },
    flush: function() { return []; }
  };
}

module.exports = {
  StyleRegistry: StyleRegistry,
  createStyleRegistry: createStyleRegistry,
  style: function style() {
    return {};
  },
  css: function css() {
    return '';
  },
  resolve: function resolve() {
    return { className: '', styles: null };
  }
};

// Default export
module.exports.default = StyleRegistry;
`;

function patchStyledJsx() {
  console.log('🔧 Applying styled-jsx React 19 compatibility patch...');

  try {
    // Find styled-jsx using enhanced path detection
    const STYLED_JSX_PATH = findStyledJsxPath();

    if (!STYLED_JSX_PATH) {
      console.log('✅ styled-jsx not found - no patching needed');
      return;
    }

    const BACKUP_PATH = STYLED_JSX_PATH + '.backup';

    // Create backup if it doesn't exist
    if (!fs.existsSync(BACKUP_PATH)) {
      const originalContent = fs.readFileSync(STYLED_JSX_PATH, 'utf8');
      fs.writeFileSync(BACKUP_PATH, originalContent);
      console.log('📦 Created backup of original styled-jsx');
    }

    // Apply the patch
    fs.writeFileSync(STYLED_JSX_PATH, EMPTY_STYLED_JSX);
    console.log('✅ styled-jsx patched successfully for React 19 compatibility');
    console.log('📝 Reason: Using Tailwind CSS + NativeWind, styled-jsx not needed');
    console.log(`📍 Patched location: ${STYLED_JSX_PATH}`);

  } catch (error) {
    console.error('❌ Failed to patch styled-jsx:', error.message);
    // Don't exit with error - graceful degradation
    console.log('⚠️  Continuing without styled-jsx patch...');
  }
}

// Run the patch
patchStyledJsx();
